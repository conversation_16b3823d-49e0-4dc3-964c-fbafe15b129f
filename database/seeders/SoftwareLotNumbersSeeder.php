<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Elabeling\EifuLotNumbersSoftware;
use Carbon\Carbon;

class SoftwareLotNumbersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sampleData = [
            [
                'lot_number' => 'SW001',
                'ref' => 'REF001',
                'product_code' => 'PC001',
                'product_name' => 'Ultrasound Software v2.1',
                'part_number' => 'PN001',
                'resource_type' => 'Software',
                'manufacture_date' => Carbon::now()->subDays(30),
                'sequence_number' => 1,
            ],
            [
                'lot_number' => 'SW002',
                'ref' => 'REF002',
                'product_code' => 'PC002',
                'product_name' => 'Sherlock Diagnostic Software v1.5',
                'part_number' => 'PN002',
                'resource_type' => 'Software',
                'manufacture_date' => Carbon::now()->subDays(15),
                'sequence_number' => 2,
            ],
            [
                'lot_number' => 'SW003',
                'ref' => 'REF003',
                'product_code' => 'PC003',
                'product_name' => 'System Shell Interface v3.0',
                'part_number' => 'PN003',
                'resource_type' => 'Software',
                'manufacture_date' => Carbon::now()->subDays(7),
                'sequence_number' => 3,
            ],
            [
                'lot_number' => 'SW004',
                'ref' => 'REF004',
                'product_code' => 'PC004',
                'product_name' => 'DICOM Communication Module v2.3',
                'part_number' => 'PN004',
                'resource_type' => 'Software',
                'manufacture_date' => Carbon::now()->subDays(3),
                'sequence_number' => 4,
            ],
            [
                'lot_number' => 'SW005',
                'ref' => 'REF005',
                'product_code' => 'PC005',
                'product_name' => 'Device Manager Software v4.1',
                'part_number' => 'PN005',
                'resource_type' => 'Software',
                'manufacture_date' => Carbon::now()->subDays(1),
                'sequence_number' => 5,
            ],
        ];

        foreach ($sampleData as $data) {
            EifuLotNumbersSoftware::create($data);
        }

        $this->command->info('Software lot numbers seeded successfully!');
    }
}
