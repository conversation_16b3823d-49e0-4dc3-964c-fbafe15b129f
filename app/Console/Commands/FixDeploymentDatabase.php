<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FixDeploymentDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:deployment-database {action=backup : Action to perform: backup, restore, or cleanup}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix deployment database issue by backing up, dropping, and restoring eifu_lot_numbers_software table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'backup':
                return $this->backupAndDrop();
            case 'restore':
                return $this->restoreData();
            case 'cleanup':
                return $this->cleanup();
            default:
                $this->error("Invalid action. Use: backup, restore, or cleanup");
                return 1;
        }
    }

    private function backupAndDrop()
    {
        $this->info('=== PHASE 1: BACKUP AND DROP ===');

        // Check if original table exists
        if (!Schema::hasTable('eifu_lot_numbers_software')) {
            $this->info('Table eifu_lot_numbers_software does not exist. Nothing to backup.');
            return 0;
        }

        $this->info('Step 1: Checking original table...');
        $originalCount = DB::table('eifu_lot_numbers_software')->count();
        $this->info("Original table has {$originalCount} rows");

        // Create backup
        $this->info('Step 2: Creating backup table...');
        try {
            DB::statement('CREATE TABLE eifu_lot_numbers_software_backup AS SELECT * FROM eifu_lot_numbers_software');
            $this->info('✅ Backup table created successfully');
        } catch (\Exception $e) {
            $this->error('❌ Failed to create backup: ' . $e->getMessage());
            return 1;
        }

        // Verify backup
        $this->info('Step 3: Verifying backup...');
        $backupCount = DB::table('eifu_lot_numbers_software_backup')->count();
        $this->info("Backup table has {$backupCount} rows");

        if ($originalCount != $backupCount) {
            $this->error('❌ Backup verification failed! Row counts do not match.');
            return 1;
        }
        $this->info('✅ Backup verified successfully');

        // Drop original table
        $this->info('Step 4: Dropping original table...');
        if ($this->confirm('Are you sure you want to drop the original table? (Backup is ready)')) {
            try {
                Schema::drop('eifu_lot_numbers_software');
                $this->info('✅ Original table dropped successfully');
            } catch (\Exception $e) {
                $this->error('❌ Failed to drop original table: ' . $e->getMessage());
                return 1;
            }
        } else {
            $this->info('Operation cancelled by user');
            return 1;
        }

        // Verify table is gone
        $this->info('Step 5: Verifying table is dropped...');
        if (!Schema::hasTable('eifu_lot_numbers_software')) {
            $this->info('✅ Confirmed: Original table has been dropped');
        } else {
            $this->error('❌ Table still exists after drop command!');
            return 1;
        }

        $this->info('');
        $this->info('🎉 PHASE 1 COMPLETE!');
        $this->info('The table has been backed up and dropped.');
        $this->info('You can now run your deployment - it should succeed!');
        $this->info('After deployment succeeds, run: php artisan fix:deployment-database restore');

        return 0;
    }

    private function restoreData()
    {
        $this->info('=== PHASE 2: RESTORE DATA ===');

        // Check if new table exists
        if (!Schema::hasTable('eifu_lot_numbers_software')) {
            $this->error('❌ New table does not exist yet. Run deployment first.');
            return 1;
        }

        // Check if backup exists
        if (!Schema::hasTable('eifu_lot_numbers_software_backup')) {
            $this->error('❌ Backup table does not exist. Cannot restore data.');
            return 1;
        }

        $this->info('Step 1: Checking tables...');
        $newCount = DB::table('eifu_lot_numbers_software')->count();
        $backupCount = DB::table('eifu_lot_numbers_software_backup')->count();
        $this->info("New table has {$newCount} rows");
        $this->info("Backup table has {$backupCount} rows");

        // Restore data
        $this->info('Step 2: Restoring data from backup...');
        try {
            DB::table('eifu_lot_numbers_software')->insert(
                DB::table('eifu_lot_numbers_software_backup')->get()->toArray()
            );
            $this->info('✅ Data restored successfully');
        } catch (\Exception $e) {
            $this->error('❌ Failed to restore data: ' . $e->getMessage());
            return 1;
        }

        // Verify restoration
        $this->info('Step 3: Verifying restoration...');
        $restoredCount = DB::table('eifu_lot_numbers_software')->count();
        $this->info("Restored table has {$restoredCount} rows");
        $this->info("Backup table has {$backupCount} rows");

        if ($restoredCount >= $backupCount) {
            $this->info('✅ Data restoration verified successfully!');
            $this->info('Run: php artisan fix:deployment-database cleanup (to remove backup table)');
        } else {
            $this->error('❌ Row counts do not match. Please verify data manually.');
            return 1;
        }

        return 0;
    }

    private function cleanup()
    {
        $this->info('=== PHASE 3: CLEANUP ===');

        if (!Schema::hasTable('eifu_lot_numbers_software_backup')) {
            $this->info('Backup table does not exist. Nothing to clean up.');
            return 0;
        }

        if ($this->confirm('Are you sure you want to delete the backup table?')) {
            try {
                Schema::drop('eifu_lot_numbers_software_backup');
                $this->info('✅ Backup table deleted successfully');
            } catch (\Exception $e) {
                $this->error('❌ Failed to delete backup table: ' . $e->getMessage());
                return 1;
            }
        } else {
            $this->info('Cleanup cancelled by user');
        }

        return 0;
    }
}
