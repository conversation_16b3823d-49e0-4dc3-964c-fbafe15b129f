<?php

namespace App\Http\Controllers\Admin\Elabeling;

use App\Http\Controllers\Controller;
use App\Models\Elabeling\EifuLotNumbersSoftware;
use App\Models\Elabeling\ElabelingResourceTypes;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;

class SoftwareLotNumbersController extends Controller
{
    /**
     * Display a listing of the software lot numbers.
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $search = $request->get('search', '');
        $perPage = $request->get('per_page', 15);
        
        $query = EifuLotNumbersSoftware::query();
        
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('lot_number', 'like', "%{$search}%")
                  ->orWhere('part_number', 'like', "%{$search}%")
                  ->orWhere('product_code', 'like', "%{$search}%")
                  ->orWhere('product_name', 'like', "%{$search}%");
            });
        }
        
        $softwareLotNumbers = $query->orderBy('created_at', 'desc')->paginate($perPage);
        
        return view('admin.elabeling.software-lot-numbers.index', compact('softwareLotNumbers', 'search', 'perPage'));
    }

    /**
     * Show the form for creating a new software lot number.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $resourceTypes = ElabelingResourceTypes::orderBy('order')->pluck('name', 'id')->all();
        
        return view('admin.elabeling.software-lot-numbers.create', compact('resourceTypes'));
    }

    /**
     * Store a newly created software lot number in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'lot_number' => 'required|string|max:25',
            'ref' => 'nullable|string|max:25',
            'product_code' => 'nullable|string|max:25',
            'product_name' => 'nullable|string|max:255',
            'part_number' => 'required|string|max:25',
            'resource_type' => 'nullable|string|max:25',
            'manufacture_date' => 'nullable|date',
            'sequence_number' => 'nullable|integer|min:0'
        ]);

        try {
            $softwareLotNumber = EifuLotNumbersSoftware::create($request->all());
            
            \Session::flash('message', 'Software lot number created successfully for lot: ' . $softwareLotNumber->lot_number);
            
            return redirect()->route('elabeling.software.lot.numbers.index');
        } catch (\Exception $e) {
            \Session::flash('error', 'Error creating software lot number: ' . $e->getMessage());
            
            return redirect()->back()->withInput();
        }
    }

    /**
     * Display the specified software lot number.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $softwareLotNumber = EifuLotNumbersSoftware::findOrFail($id);
        
        return view('admin.elabeling.software-lot-numbers.show', compact('softwareLotNumber'));
    }

    /**
     * Show the form for editing the specified software lot number.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $softwareLotNumber = EifuLotNumbersSoftware::findOrFail($id);
        $resourceTypes = ElabelingResourceTypes::orderBy('order')->pluck('name', 'id')->all();
        
        return view('admin.elabeling.software-lot-numbers.edit', compact('softwareLotNumber', 'resourceTypes'));
    }

    /**
     * Update the specified software lot number in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $softwareLotNumber = EifuLotNumbersSoftware::findOrFail($id);
        
        $request->validate([
            'lot_number' => 'required|string|max:25',
            'ref' => 'nullable|string|max:25',
            'product_code' => 'nullable|string|max:25',
            'product_name' => 'nullable|string|max:255',
            'part_number' => 'required|string|max:25',
            'resource_type' => 'nullable|string|max:25',
            'manufacture_date' => 'nullable|date',
            'sequence_number' => 'nullable|integer|min:0'
        ]);

        try {
            $softwareLotNumber->update($request->all());
            
            \Session::flash('message', 'Software lot number updated successfully for lot: ' . $softwareLotNumber->lot_number);
            
            return redirect()->route('elabeling.software.lot.numbers.index');
        } catch (\Exception $e) {
            \Session::flash('error', 'Error updating software lot number: ' . $e->getMessage());
            
            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified software lot number from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $softwareLotNumber = EifuLotNumbersSoftware::findOrFail($id);
            $lotNumber = $softwareLotNumber->lot_number;
            
            $softwareLotNumber->delete();
            
            \Session::flash('message', 'Software lot number deleted successfully for lot: ' . $lotNumber);
            
            return redirect()->route('elabeling.software.lot.numbers.index');
        } catch (\Exception $e) {
            \Session::flash('error', 'Error deleting software lot number: ' . $e->getMessage());
            
            return redirect()->back();
        }
    }

    /**
     * Change the status of the specified software lot number.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeStatus(Request $request, $id)
    {
        try {
            $softwareLotNumber = EifuLotNumbersSoftware::withTrashed()->findOrFail($id);
            
            if ($softwareLotNumber->trashed()) {
                $softwareLotNumber->restore();
                $status = 'activated';
            } else {
                $softwareLotNumber->delete();
                $status = 'deactivated';
            }
            
            return response()->json([
                'success' => true,
                'message' => "Software lot number {$status} successfully",
                'status' => $status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error changing status: ' . $e->getMessage()
            ], 500);
        }
    }
}
