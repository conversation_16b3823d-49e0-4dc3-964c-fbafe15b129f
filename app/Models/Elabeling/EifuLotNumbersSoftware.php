<?php

namespace App\Models\Elabeling;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EifuLotNumbersSoftware extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'eifu_lot_numbers_software';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'lot_number',
        'ref',
        'product_code',
        'product_name',
        'part_number',
        'resource_type',
        'manufacture_date',
        'sequence_number'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'manufacture_date' => 'date',
        'sequence_number' => 'integer'
    ];

    /**
     * The dates protected and treated as Carbon instances
     *
     * @var array
     */
    protected $dates = ['deleted_at', 'manufacture_date'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    /**
     * Scope to search by lot number
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $lotNumber
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByLotNumber($query, $lotNumber)
    {
        return $query->where('lot_number', $lotNumber);
    }

    /**
     * Scope to search by part number
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $partNumber
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByPartNumber($query, $partNumber)
    {
        return $query->where('part_number', $partNumber);
    }

    /**
     * Scope to search by product code
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $productCode
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByProductCode($query, $productCode)
    {
        return $query->where('product_code', $productCode);
    }

    /**
     * Get formatted manufacture date
     *
     * @return string
     */
    public function getFormattedManufactureDateAttribute()
    {
        return $this->manufacture_date ? $this->manufacture_date->format('Y-m-d') : '';
    }
}
