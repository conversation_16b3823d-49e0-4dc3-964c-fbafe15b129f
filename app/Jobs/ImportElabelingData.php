<?php

namespace App\Jobs;

use App\Mail\MailableTemplate;
use App\Models\Elabeling\ElabelingImportDetail;
use App\Models\Elabeling\ElabelingImportLog;
use Config;
use DB;
use Exception;
use File;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;
use Mail;
use Schema;

// use ZipArchive;

class ImportElabelingData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // Original Source Import filename, directory and path
    protected $source_import_directory;

    protected $source_import_basename;

    protected $source_import_filename;

    protected $source_import_extension;

    protected $source_import_file_path;

    protected $source_import_filename_parts;

    // Renamed and Datestamped Import filename, directory and path
    protected $datestamp_import_filename;

    protected $datestamp_import_file_path;

    // Archive / Log filename, directory and path
    protected $import_processed_archive_directory;

    protected $import_log_filename;

    protected $import_log_file_path;

    protected object $import_detail;

    protected $import_map;

    protected $import_log_insert_id;

    protected $processed;

    // record process counters
    protected $success;

    protected $errors;

    protected $ending_db_sequence_number;

    protected $starting_sequence_number;

    protected $ending_sequence_number;

    protected $continuous_data_stream;

    protected $output = [];

    protected $notification;

    protected $temporary_table_name;

    protected $admin_emails;

    protected $import_database_table;

    protected $import_lot_number_tables = ['REYIFUCOMP' => 'eifu_lot_numbers_mfgpro', 'JDEIFUCOMP' => 'eifu_lot_numbers_jde', 'SOFTWARECOMP' => 'eifu_lot_numbers_software'];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        // 		Log::info('Construct Fired');
        // load from Config file
        $this->source_import_directory = Config::get('settings.elabeling.ftp_directory');
        $this->import_processed_archive_directory = Config::get('settings.elabeling.import_processed_archive_directory');

        $this->import_map = [];
        $this->processed = false;
        $this->success = 0;
        $this->errors = 0;
        $this->admin_emails = ['<EMAIL>', '<EMAIL>'];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->output[] = 'Notifications: ';

        Log::info($this->source_import_directory);

        // Get the file from the directory - set the file path
        if (File::isDirectory($this->source_import_directory)) {    // check that the filepath is a directory

            $files = File::files($this->source_import_directory);    // gets all the files in the directory and puts them in an array

            if (count($files) > 0) {
                foreach ($files as $file) {
                    if ($file) { // checks to make sure there is at least 1 file in array

                        // dd($file);
                        // SplFileInfo {#648 ▼
                        //   -relativePath: ""
                        //   -relativePathname: "JDEIFUCOMP_20170930_081502.csv"
                        //   path: "C:\Users\<USER>\Documents\webserver\wampstack\frameworks\bardaccess\storage\ftp\elabeling\"
                        //   filename: "JDEIFUCOMP_20170930_081502.csv"
                        //   basename: "JDEIFUCOMP_20170930_081502.csv"
                        //   pathname: "C:\Users\<USER>\Documents\webserver\wampstack\frameworks\bardaccess\storage\ftp\elabeling\\JDEIFUCOMP_20170930_081502.csv"
                        //   extension: "csv"
                        //   realPath: "C:\Users\<USER>\Documents\webserver\wampstack\frameworks\bardaccess\storage\ftp\elabeling\JDEIFUCOMP_20170930_081502.csv"
                        //   aTime: 2017-10-06 16:50:48
                        //   mTime: 2017-10-06 16:50:48
                        //   cTime: 2017-10-06 16:50:48
                        //   inode: 0
                        //   size: 1082
                        //   perms: 0100666
                        //   owner: 0
                        //   group: 0
                        //   type: "file"
                        //   writable: true
                        //   readable: true
                        //   executable: false
                        //   file: true
                        //   dir: false
                        //   link: false
                        //   linkTarget: "C:\Users\<USER>\Documents\webserver\wampstack\frameworks\bardaccess\storage\ftp\elabeling\JDEIFUCOMP_20170930_081502.csv"
                        // }

                        $this->source_import_filename = pathinfo($file, PATHINFO_FILENAME);  // "JDEIFUCOMP_20170930_081502"  // filename without extension
                        $this->source_import_basename = pathinfo($file, PATHINFO_BASENAME);  // "JDEIFUCOMP_20170930_081502.csv"  // filename with extension
                        $this->source_import_extension = pathinfo($file, PATHINFO_EXTENSION); // "csv"

                        // check to make sure the file exists
                        if (File::exists($file)) {
                            Log::info('This file was found: '.$file);

                            // raw file name and path with timestamp
                            $this->source_import_file_path = $this->source_import_directory.$this->source_import_basename; // "C:\Users\<USER>\Documents\webserver\wampstack\frameworks\bardaccess\storage\ftp\elabeling\JDEIFUCOMP_20170930_081502.csv"

                            // strip off timestamp from filename used for file verification process isValid().
                            $this->source_import_filename_parts = explode('-', str_replace('_', '-', $this->source_import_filename)); // break up the filename // replace _ underscores with dashes in the filename

                            $this->datestamp_import_filename = date('Ymd_His').'_'.$this->source_import_basename; // "20171006_170426_JDEIFUCOMP_20170930_081502.csv"
                            $this->datestamp_import_file_path = $this->source_import_directory.$this->datestamp_import_filename; // "C:\Users\<USER>\Documents\webserver\wampstack\frameworks\bardaccess\storage\ftp\elabeling\20171006_170320_JDEIFUCOMP_20170930_081502.csv"

                            // from function setImportLogDirectory($import_processed_archive_directory)
                            $this->import_log_filename = $this->datestamp_import_filename; // "20171010_084635_JDEIFUCOMP_20170930_081502.csv"
                            $this->import_log_file_path = $this->import_processed_archive_directory.$this->import_log_filename;  // "C:\Users\<USER>\Documents\webserver\wampstack\frameworks\bardaccess\storage\app\uploads\elabeling\processed\20171010_084717_JDEIFUCOMP_20170930_081502.csv"
                            // dd($this->source_import_filename);

                            // get import detail

                            $this->import_detail = $this->getImportDetail($this->source_import_filename);

                            // per the SRS report
                            $this->output[] = 'The import process has scanned the import directory and found a valid file.';

                            // Check to make sure that the import has not already been run
                            if ($this->checkIfIsUnique($this->source_import_basename)) {

                                // Get the ending sequence number from the last elabeling lot numbers database before import.
                                $this->ending_db_sequence_number = DB::table($this->import_detail->database_table)->orderBy('sequence_number', 'desc')->value('sequence_number');

                                // try to load the import map
                                $this->loadImportMap();
                                $this->output[] = 'The import process has loaded an import map for the CSV file.';

                                // make sure we have fields mapped, otherwise we wont be updating anything at all
                                if ($this->import_map) {
                                    $processed = $this->importFile($this->source_import_file_path);

                                    // if import processed zip and archive file then notify website admins
                                    if ($processed) {
                                        $this->output[] = 'The import process has scanned the CSV file and imported any data into the database.';

                                        // try to rename the file
                                        $this->addDateTimeToFileName();

                                        // Try to backup the databases here
                                        // $this->backupTables();
                                        // $this->output[] = 'The import process has backed up the tables used.';

                                        // try to open and rename the file
                                        // $this->openAndAddImportFiletoZipArchive();
                                        // $this->output[] = 'The import process has zipped and archived the import file.';

                                        // try to log the import process -> includes loading existing table
                                        $this->logImportToDatabase();
                                        $this->output[] = 'The import process has logged the import in the database.';
                                    } else {
                                        $this->output[] = 'There was a problem importing the file.';
                                    }

                                    // else if the fields are mapped
                                } else {
                                    $this->output[] = 'No field mapping has been done for the '.$this->import_detail->name.' import.';
                                }
                            } else {
                                // return errors - send off some sort of notification in the output
                                $this->output[] = 'The import process was not run because the file has already been imported.';
                                Log::info('The import process was not run because the file has already been imported.');
                            } // end check if is unique
                        } else {
                            $this->output[] = 'Invalid Filepath';

                            return false;
                        } // end if file doesn't exist
                    } else { // end if there's not a ffile in the directory
                        $this->output[] = 'There is not a file to run for the E-labeling import process.';
                        Log::info('There is not a file to run for the E-labeling import process.');

                        return false;
                    }

                    // Send email notification
                    $this->finalizeAndSendEmail();
                } //end foreach
            } else {
                Log::info('There are no files for the E-labeling import process to run.');
            } // end if files count > 0 check
        } // end if the directory is htere

        return 'Elabeling Job ran';
    }

    ///********
    // FIRE FUNCTIONS
    ///********

    /**
     * Is import file valid
     *
     * @return bool
     */
    public function importFile($file_path)
    {
        set_time_limit(600);
        ini_set('memory_limit', '512M');

        // If the fields are mapped, check to see that you can open the file
        if (($fp = fopen($file_path, 'r')) !== false) {

            // first -> let's create the multidimensional array we'll use for the upload
            $row = 0;
            $rowbuilder = [];
            $all_rows_as_multidimensional_array = [];
            while (($data = fgetcsv($fp, 8192, '^')) !== false) {
                for ($x = 0; $x < count($this->import_map); $x++) {
                    try {
                        $value = isset($data[intval($this->import_map[$x]->column_index)]) ? $data[intval($this->import_map[$x]->column_index)] : '';
                        $value = trim($value);
                    } catch (\Exception $e) {
                        // print_r($data);
                    }

                    if ($row == 1 && $this->import_map[$x]->database_column == 'sequence_number') { // get first row starting sequence number
                        $this->starting_sequence_number = $value; // Set starting sequence number
                    } else {
                        $this->ending_sequence_number = $value; // Set the ending sequence number to the current sequence number.
                    }

                    if ($this->import_map[$x]->database_column == 'product_code') {
                        $value = rtrim($value, 'NS'); // Remove the "NS" (Non-sterile indicator) from the end upon import
                    }

                    if ($this->import_map[$x]->database_column == 'part_number') {
                        $value = ltrim($value, 'PK'); // Remove the "PK" from the front upon import
                        $value = ltrim($value, 'RM'); // Remove the "RM" from the front upon import
                    }

                    if ($this->import_map[$x]->database_column == 'manufacture_date') {
                        $value = date('Y-m-d', strtotime(str_replace('-', '/', $value)));
                    }

                    $rowbuilder[$this->import_map[$x]->database_column] = $value;
                }
                $all_rows_as_multidimensional_array[$row] = $rowbuilder;
                $row++;
            }

            // second -> let's process through each of those arrays in the multidimensional one, inserting them into the database
            foreach ($all_rows_as_multidimensional_array as $single_row_as_array) {
                if ($all_rows_as_multidimensional_array[0] == $single_row_as_array) {
                    // dont add titles to the database
                } else {
                    try {
                        $single_row_as_array = array_add($single_row_as_array, 'created_at', date('Y-m-d g:i:s'));
                        $single_row_as_array = array_add($single_row_as_array, 'updated_at', date('Y-m-d g:i:s'));

                        // $db = DB::connection(); $recordChanged = $db->statement($recordChangeQuery);
                        $commit = DB::table($this->import_detail->database_table)->insertGetId(
                            $single_row_as_array // array of values
                        );

                        // count so it can be displayed on the notifications
                        $this->success++;
                    } catch (Exception $e) {
                        $this->output[] = 'There was an error attempting to import record sequenced number '.$single_row_as_array['sequence_number'].' from Import with a part number of '.$single_row_as_array['part_number'];

                        // count so it can be displayed on the notifications
                        $this->errors++;
                    }
                }
            }

            fclose($fp);

            $this->processed = true;

            return true;

            // else if the fields are mapped, but you can't open the file
        } else {
            $this->output[] = 'error opening: '.$this->datestamp_import_file_path;
        }
    }

    /**
     * Is import file valid
     *
     * @return bool
     */
    public function checkIfIsUnique($filename)
    {

        // see if the file has already been logged
        $log = DB::table('eifu_import_log')->where('logged_filename', $filename)->first();

        if ($log) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * get import DB table name
     *
     * @return string
     */
    public function getImportDetail($filename)
    {
        $filename_parts = explode('-', str_replace('_', '-', $filename)); // break up the filename // replace _ underscores with dashes in the filename

        return ElabelingImportDetail::where('filename', $filename_parts[0].'.'.$this->source_import_extension)->first();
    }

    /**
     * Try to rename the original import file and add a datestamp so that it's only imported once.
     *
     * @return bool
     */
    public function addDateTimeToFileName()
    {
        try {
            // move file from
            // "C:\Users\<USER>\Documents\webserver\wampstack\frameworks\bardaccess\storage\ftp\elabeling\JDEIFUCOMP_20170930_081502.csv"
            // To
            // "C:\Users\<USER>\Documents\webserver\wampstack\frameworks\bardaccess\storage\ftp\elabeling\20171010_101957_JDEIFUCOMP_20170930_081502.csv"
            File::move($this->source_import_file_path, $this->datestamp_import_file_path);

            return true;
        } catch (Exception $e) {
            Log::error($e);
            $this->output[] = $this->source_import_file_path.' could not be moved and renamed to: '.$this->datestamp_import_file_path.' - Therefore, may not have been archived.';
            Log::info($this->source_import_file_path.' could not be renamed to: '.$this->datestamp_import_file_path.' - Therefore, may not have been archived.');

            return false;
        }
    }

    /**
     * Log import file
     * Zip and archive the import file
     *
     * @return bool
     */
    // public function openAndAddImportFiletoZipArchive()
    // {

    //     //create the zip object
    //     $this->zip = new ZipArchive;

    //     // try to open the zip and add the import
    //     try {
    //         $this->zip->open($this->import_log_file_path . '.zip', ZIPARCHIVE::CREATE);

    //         try { // to add a file to the zip
    //             $this->zip->addFile($this->datestamp_import_file_path, $this->import_log_filename);
    //             $this->zip->close();

    //             return true;
    //         } catch (Exception $e) { // can't add file to the zip
    //             $this->output[] = $this->datestamp_import_file_path . ' could not be copied to: ' . $this->import_log_file_path . '. Therefore, not logged, not processed.';

    //             return false;
    //         }
    //     } catch (Exception $e) { // can't open the zip
    //         // catch the error
    //         $this->output[] = 'Cannot open ' . $this->import_log_file_path . '';

    //         return false;
    //     }
    // }

    /**
     * Log import file
     * Zip and archive the import file
     *
     * @return bool
     */
    public function logImportToDatabase()
    {
        try { // to log the process in the database
            $log_file = ElabelingImportLog::insertGetId(['logged_filename' => $this->source_import_basename, 'created_at' => date('Y-m-d g:i:s')]);
            $this->import_log_insert_id = $log_file;

            return true;
        } catch (Exception $e) { // can't update the db
            $this->output[] = 'Something went wrong when attempting to log this import in the database';

            return false;
        }
    }

    /**
     * Update import log
     *
     * @return bool
     */
    public function updateImportLog($id, $success, $errors, $starting_sequence_number, $ending_sequence_number)
    {
        try { // to log the process in the database
            ElabelingImportLog::where('id', $id)->update([
                'successful' => $success,
                'errors' => $errors,
                'starting_sequence_number' => $starting_sequence_number,
                'ending_sequence_number' => $ending_sequence_number,

            ]);
        } catch (Exception $e) { // can't update the db
            $this->output[] = 'Something went wrong when attempting to update the import log';

            return false;
        }
    }

    /**
     * Load existing tables
     *
     * @return array
     */
    public function loadImportMap()
    {
        try {
            $mapfields = $this->import_detail->map()->orderBy('sort_order')->get();

            foreach ($mapfields as $mapfield) {
                $this->import_map[] = $mapfield;
            }
        } catch (Exception $e) {
            $this->output[] = 'Loading of import map failed.';

            return false;
        }

        // if no error
        return true;
    }

    /**
     * Finalize import process
     */
    public function finalizeAndSendEmail()
    {
        // 		Log::info('Finalizing and Sending Email');

        $finalMessage = '';

        // delete the file, if it has been logged & processed
        if ($this->processed) {
            if (unlink($this->datestamp_import_file_path)) {
                // do nothing
            } else {
                $this->output[] = $this->datestamp_import_file_path.' could not be deleted.';
            }

            // update the import log table with the number of success and errored rows that were imported
            if (is_int($this->starting_sequence_number) && is_int($this->ending_sequence_number)) {
                $this->updateImportLog($this->import_log_insert_id, $this->success, $this->errors, $this->starting_sequence_number, $this->ending_sequence_number);
            } else {
                $this->updateImportLog($this->import_log_insert_id, $this->success, $this->errors, null, null);
            }

            $this->continuous_data_stream = (($this->ending_db_sequence_number + 1) == $this->starting_sequence_number) ? 'Yes' : 'No';

            $notifications = implode('<br>', $this->output);
            $finalMessage = 'The '.$this->import_detail->name.' import completed '.date('m/d/Y').'.<br/>
								<strong>Successful</strong>: '.$this->success.'<br/>
								<strong>Errors</strong>: '.$this->errors.'<br/>
								<strong>Ending Sequence Number From Previous Import</strong>: '.$this->ending_db_sequence_number.'<br/>
								<strong>Starting Sequence Number</strong>: '.$this->starting_sequence_number.'<br/>
								<strong>Ending Sequence Number</strong>: '.$this->ending_sequence_number.'<br/>
								<strong>Import Data Continuous</strong>: '.$this->continuous_data_stream.'<br/><br/>
								<br>'.$notifications.'<br>';

            $fromEmail = $this->import_detail->notify_from_email;
            $fromSubject = $this->import_detail->name;

            // Send the mail
            $data = ['output' => $finalMessage];

            try {
                $subject = $fromSubject.' Import Process Completed';
                $template = 'emails.elabeling-import-results';

                Mail::to($this->admin_emails)->send(new MailableTemplate($template, $data, $subject));
            } catch (Exception $e) {
                // Send do download page
                Log::info('ELabeling Import Results Mail failed to send.'.$e);
            }
        } else {
            $finalMessage = 'The elabeling import process ran, but there was no file to import, as the previous job ran the following processes without error<br/>';
            // $finalMessage .= 'The check for continuity, valid file types, and column types/order before importing the data successfully.<br/>';
            // $finalMessage .= 'The import process backed-up the database, zipped and archived the previous import file before deleting the file from the directory before logging the import.<br/>';
            // $fromEmail = '<EMAIL>';
            // $fromSubject = 'ELabeling';
        } // end if processed

        // Reset import metrics when finished.
        $this->success = 0;
        $this->errors = 0;
        $this->output = [];

        // return the final output back
        return $finalMessage;
    }

    /**
     * Load existing tables
     *
     * @return array
     */
    public function loadExistingTables()
    {
        $existing_tables = [];
        $allTables = DB::select('SHOW TABLES');
        $dbName = Config::get('database.connections.mysql.database');
        $rowName = 'Tables_in_'.$dbName;

        foreach ($allTables as $table) {
            $existing_tables[] = $table->$rowName;
        }

        return $existing_tables;
    }

    /**
     * Backup import tables
     * Keeps a 3 day rolling backup
     */
    public function backupTables()
    {
        try {

            // backup databases here
            if (intval($this->import_detail->backup_count) > 0) {

                // load the tables at the start
                $existing_tables = $this->loadExistingTables();

                // check to see if the oldest backup tables exists
                if (in_array($this->import_detail->backup_table_prefix.intval($this->import_detail->backup_count), $existing_tables)) {
                    // if so, drop it like it's hot
                    Schema::dropIfExists($this->import_detail->backup_table_prefix.intval($this->import_detail->backup_count));
                }

                // start at the number of backups to do
                for ($x = intval($this->import_detail->backup_count); $x > 1; $x--) {
                    // reload all of the tables returns an array of all the table names
                    $existing_tables = $this->loadExistingTables();

                    // since we checck $x-1, the backup_0 table will never exist, so it wont run, we have to this one manually, that is why we stop before $x = 1
                    // we also hve to run it manually because we are using a different value to rename the table from ( database_table not backup_table_prefix )
                    $old_table_name = $this->import_detail->backup_table_prefix.($x - 1);
                    $new_table_name = $this->import_detail->backup_table_prefix.$x;

                    if (in_array($old_table_name, $existing_tables)) {
                        // rename them accordingly
                        Schema::rename($old_table_name, $new_table_name);
                    }
                }

                // Copy the current real table to the first backup
                $db = DB::connection();

                $sql = 'CREATE TABLE IF NOT EXISTS '.$this->import_detail->backup_table_prefix.'1 LIKE '.$this->import_detail->database_table.'';
                $db->statement($sql);

                $sql = 'INSERT IGNORE INTO '.$this->import_detail->backup_table_prefix.'1 SELECT * FROM '.$this->import_detail->database_table.'';
                $db->statement($sql);
            } else {
                $this->output[] = 'There backup count is set in the database incorrectly.';

                return false;
            }
        } catch (Exception $e) {
            $this->output[] = 'There was a problem backup in the tables.';

            return false;
        }

        return true;
    }

    // end class
}
