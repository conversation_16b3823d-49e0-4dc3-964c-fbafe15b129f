<?php
$category_header = $title = 'Device Matrix Management';
$menu = 'device_matrix';
?>

@extends('layouts.admin.master')

@section('title', 'Device Matrix Management')

@section('styles')
<style>
    /* Environment Toggle */
    .environment-toggle {
        margin-bottom: 20px;
    }
    .environment-toggle .btn {
        margin-right: 10px;
        border-radius: 6px;
        font-weight: 500;
        padding: 8px 16px;
        transition: all 0.2s ease;
    }

    /* Search Form */
    .search-form {
        margin-bottom: 20px;
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    /* Filter Row */
    .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
        align-items: flex-end;
    }
    .filter-row .form-group {
        flex: 1;
        min-width: 200px;
        margin-bottom: 0;
    }
    .filter-row .form-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    /* Action Buttons - Fixed horizontal layout */
    .action-buttons {
        margin-bottom: 20px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;
        padding: 15px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .action-buttons .btn,
    .action-buttons .btn-group {
        margin-right: 0;
        margin-bottom: 0;
        flex-shrink: 0;
    }
    .action-buttons .btn {
        color: white !important;
        white-space: nowrap;
        border-radius: 6px;
        font-weight: 500;
        padding: 8px 16px;
        transition: all 0.2s ease;
    }
    /* Table Styling */
    .table-responsive {
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        background: white;
        max-height: calc(100vh - 400px);
        overflow-y: auto;
    }
    .table th, .table td {
        padding: 12px 8px;
        vertical-align: middle;
        border-bottom: 1px solid #dee2e6;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 2px 3px rgba(0,0,0,0.1);
    }

    /* Pagination */
    .pagination {
        margin-top: 20px;
        justify-content: center;
    }

    /* Bulk Actions */
    .bulk-actions {
        margin-top: 20px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }
    .bulk-actions .form-group {
        margin-bottom: 15px;
    }
    .bulk-actions h5 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 15px;
    }

    /* Environment Badges */
    .badge-environment {
        font-size: 12px;
        padding: 6px 12px;
        margin-left: 10px;
        border-radius: 20px;
        font-weight: 500;
    }
    .badge-production {
        background-color: #dc3545;
        color: white;
    }
    .badge-uat {
        background-color: #28a745;
        color: white;
    }

    /* Button Colors */
    .btn-primary, .btn-success, .btn-info, .btn-warning, .btn-danger {
        color: white !important;
    }
    .btn-sm {
        color: white !important;
    }
    
    /* Selection styling */
    .selection-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 15px;
        border-radius: 4px;
    }
    
    .selected-count-text {
        font-weight: bold;
    }
    
    /* Highlight selected rows */
    tr.selected-row {
        background-color: #e8f4ff !important;
    }
    
    /* Dropdown styling */
    .dropdown-item {
        padding: 8px 20px;
    }
    
    .dropdown-item:hover {
        background-color: #f8f9fa;
    }
    
    /* Search input styling */
    .input-group-text {
        background-color: #f8f9fa;
        border-right: none;
    }
    
    .input-group .form-control {
        border-left: none;
    }
    
    .input-group .form-control:focus {
        box-shadow: none;
        border-color: #ced4da;
    }
    
    .input-group .form-control:focus + .input-group-append .input-group-text,
    .input-group .form-control:focus ~ .input-group-prepend .input-group-text {
        border-color: #ced4da;
    }
    
    .clear-search {
        border-color: #ced4da;
        background-color: white;
    }
    
    .clear-search:hover {
        background-color: #f8f9fa;
    }
    
    /* Input Group Fixes - Bootstrap 3 Compatible */
    .input-group {
        position: relative;
        display: table;
        border-collapse: separate;
    }
    .input-group .form-control {
        position: relative;
        z-index: 2;
        float: left;
        width: 100%;
        margin-bottom: 0;
    }
    .input-group-addon {
        padding: 6px 12px;
        font-size: 14px;
        font-weight: normal;
        line-height: 1;
        color: #555;
        text-align: center;
        background-color: #f8f9fa;
        border: 1px solid #ccc;
        border-radius: 4px;
        width: 1%;
        white-space: nowrap;
        vertical-align: middle;
        display: table-cell;
    }
    .input-group .form-control:first-child,
    .input-group-addon:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
    .input-group .form-control:last-child,
    .input-group-addon:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    .input-group-addon:first-child {
        border-right: 0;
    }
    .input-group-addon:last-child {
        border-left: 0;
    }
    
    /* Keyboard shortcut styling */
    .keyboard-shortcut {
        font-size: 80%;
        margin-left: 5px;
        opacity: 0.7;
    }
    
    .keyboard-shortcut kbd {
        display: inline-block;
        padding: 2px 4px;
        font-size: 90%;
        font-weight: 700;
        line-height: 1;
        color: #212529;
        background-color: #f8f9fa;
        border: 1px solid #ced4da;
        border-radius: 3px;
        box-shadow: 0 1px 0 rgba(0,0,0,0.2);
    }
    
    /* Search icon animation */
    @keyframes searchPulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.7; }
        100% { transform: scale(1); opacity: 1; }
    }
    
    .search-pulse {
        animation: searchPulse 1.5s ease-in-out;
    }
    
    /* Sticky table header */
    .sticky-header {
        position: relative;
    }
    
    .sticky-header thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 10;
        box-shadow: 0 2px 3px rgba(0,0,0,0.1);
    }
    
    /* Ensure the table header is visible when scrolling */
    .table-responsive {
        max-height: calc(100vh - 350px);
        overflow-y: auto;
    }
    
    /* Table hover effects */
    .table-hover tbody tr:hover {
        background-color: #f1f8ff;
        transition: background-color 0.2s ease;
    }
    
    /* Loading Overlay - Fixed positioning and auto-hide */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255,255,255,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        visibility: hidden;
        opacity: 0;
        transition: visibility 0s linear 0.3s, opacity 0.3s linear;
    }

    .loading-overlay.show {
        visibility: visible;
        opacity: 1;
        transition: visibility 0s linear 0s, opacity 0.3s linear;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
        border: 1px solid #e9ecef;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 15px;
    }

    .loading-text {
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Filter Actions - Improved layout */
    .filter-actions {
        display: flex;
        align-items: flex-end;
        margin-bottom: 20px;
        gap: 20px;
        flex-wrap: wrap;
    }

    .filter-buttons {
        display: flex;
        gap: 10px;
        flex-shrink: 0;
    }

    .quick-filter-container {
        flex: 1;
        max-width: 350px;
        min-width: 250px;
    }

    .quick-filter-container label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
        display: block;
    }

    .quick-filter-highlight {
        background-color: #fff3cd !important;
        color: #856404 !important;
        font-weight: 600;
        padding: 1px 3px;
        border-radius: 3px;
    }

    /* Quick Filter Visual Differentiation */
    .quick-filter-container .input-group {
        border: 2px solid #17a2b8;
        border-radius: 6px;
        overflow: hidden;
    }

    .quick-filter-container .input-group-addon {
        background-color: #17a2b8;
        color: white;
        border: none;
        border-radius: 0;
    }

    .quick-filter-container .form-control {
        border: none;
        border-radius: 0;
    }

    .quick-filter-container .form-control:focus {
        box-shadow: none;
        border-color: transparent;
    }
    
    /* No results message */
    .no-results-message {
        display: none;
        padding: 20px;
        text-align: center;
        background-color: #f8f9fa;
        border-radius: 5px;
        margin-top: 20px;
    }
    
    /* Back to top button */
    .back-to-top {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #007bff;
        color: white;
        text-align: center;
        line-height: 40px;
        font-size: 20px;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s, visibility 0.3s;
        z-index: 999;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .back-to-top.show {
        opacity: 1;
        visibility: visible;
    }
    
    .back-to-top:hover {
        background-color: #0069d9;
    }
    
    /* Table sorting indicators */
    .sortable {
        cursor: pointer;
        position: relative;
        padding-right: 20px !important;
    }
    
    .sortable:after {
        content: '\f0dc';
        font-family: FontAwesome;
        position: absolute;
        right: 5px;
        color: #ccc;
    }
    
    .sortable.asc:after {
        content: '\f0de';
        color: #007bff;
    }
    
    .sortable.desc:after {
        content: '\f0dd';
        color: #007bff;
    }

    /* Modern UI Improvements */
    .form-control {
        border-radius: 6px;
        border: 1px solid #ced4da;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn {
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    /* New Header Section Styles */
    .matrix-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 8px;
        margin-bottom: 25px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .header-main {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .environment-selector {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .environment-label {
        font-weight: 600;
        margin: 0;
        font-size: 16px;
    }

    .environment-toggle .btn:not(.btn-danger):not(.btn-success) {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .header-actions {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .primary-action {
        font-size: 16px;
        padding: 12px 24px;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .quick-stats {
        display: flex;
        gap: 30px;
        padding-top: 20px;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        display: block;
        font-size: 24px;
        font-weight: bold;
        line-height: 1;
    }

    .stat-label {
        display: block;
        font-size: 12px;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Improved Search Section */
    .search-filter-section {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 25px;
        margin-bottom: 25px;
    }

    .primary-search {
        margin-bottom: 20px;
    }

    .search-input-group {
        display: flex;
        gap: 10px;
        align-items: flex-end;
    }

    .search-input-group .input-group {
        flex: 1;
    }

    .advanced-filters {
        border-top: 1px solid #dee2e6;
        padding-top: 20px;
    }

    .filter-toggle {
        color: #495057;
        text-decoration: none;
        font-weight: 600;
    }

    .filter-toggle:hover {
        color: #007bff;
        text-decoration: none;
    }

    .filter-count {
        color: #28a745;
        font-weight: bold;
    }

    .filter-panel {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 20px;
        margin-top: 15px;
    }

    /* Results Section */
    .results-section {
        margin-bottom: 25px;
    }

    .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e9ecef;
    }

    .results-info h4 {
        margin: 0 0 5px 0;
        color: #495057;
    }

    .results-count {
        color: #6c757d;
        font-size: 14px;
    }

    .selection-bar {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 6px;
        padding: 12px 20px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .selection-info {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #1976d2;
        font-weight: 600;
    }

    .selection-actions {
        display: flex;
        gap: 10px;
    }

    /* Bulk Actions Panel */
    .bulk-actions-panel {
        margin: 20px 0;
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .bulk-actions-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #dee2e6;
    }

    .selected-summary {
        color: #495057;
        font-weight: 600;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Card-like sections */
    .card-section {
        background: white;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    /* Improved spacing */
    .main-content {
        padding: 20px;
    }

    /* Better visual hierarchy */
    h1 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 20px;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .filter-row {
            flex-direction: column;
        }

        .filter-row .form-group {
            min-width: 100%;
        }

        .action-buttons {
            flex-direction: column;
            align-items: stretch;
        }

        .action-buttons .btn,
        .action-buttons .btn-group {
            margin-bottom: 10px;
        }

        .filter-actions {
            flex-direction: column;
            align-items: stretch;
        }

        .quick-filter-container {
            max-width: 100%;
            margin-top: 15px;
        }
    }
</style>
@endsection

@section('content')
<!-- Loading Overlay -->
<div class="loading-overlay">
    <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading...</div>
    </div>
</div>

<!-- Back to Top Button -->
<div class="back-to-top" title="Back to top">
    <i class="fa fa-arrow-up"></i>
</div>

<!-- Keyboard Shortcuts Help Modal -->
<div class="modal fade" id="keyboardShortcutsModal" tabindex="-1" role="dialog" aria-labelledby="keyboardShortcutsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="keyboardShortcutsModalLabel">
                    <i class="fa fa-keyboard-o"></i> Keyboard Shortcuts
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Shortcut</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><kbd>Ctrl</kbd> / <kbd>⌘</kbd> + <kbd>F</kbd></td>
                            <td>Focus the search field</td>
                        </tr>
                        <tr>
                            <td><kbd>Alt</kbd> / <kbd>⌥</kbd> + <kbd>Q</kbd></td>
                            <td>Focus the quick filter</td>
                        </tr>
                        <tr>
                            <td><kbd>Esc</kbd></td>
                            <td>Clear the current filter field</td>
                        </tr>
                        <tr>
                            <td><kbd>?</kbd></td>
                            <td>Show this help dialog</td>
                        </tr>
                        <tr>
                            <td><kbd>Ctrl</kbd> / <kbd>⌘</kbd> + <kbd>A</kbd></td>
                            <td>Select all entries</td>
                        </tr>
                        <tr>
                            <td><kbd>Ctrl</kbd> / <kbd>⌘</kbd> + <kbd>D</kbd></td>
                            <td>Deselect all entries</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="content_hldr">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>
                    Device Matrix Management
                    @if($environment === 'production')
                    <span class="badge badge-environment badge-production">Production</span>
                    @else
                    <span class="badge badge-environment badge-uat">UAT/Staging</span>
                    @endif
                </h1>
            </div>
        </div>
    </div>
    
    <div id="content">
        @include('admin/software/includes/sidebar')
        
        <div id="pagecontent">
            <div id="main" class="main-content">
                @include('flash::message')
                
                <!-- Header Section with Environment and Quick Actions -->
                <div class="matrix-header">
                    <div class="header-main">
                        <div class="environment-selector">
                            <label class="environment-label">Environment:</label>
                            <div class="btn-group environment-toggle" role="group">
                                <a href="{{ route('device.matrix.index', ['environment' => 'production']) }}"
                                   class="btn {{ $environment === 'production' ? 'btn-danger' : 'btn-outline-danger' }}">
                                    <i class="fa fa-server"></i> Production
                                </a>
                                <a href="{{ route('device.matrix.index', ['environment' => 'uat']) }}"
                                   class="btn {{ $environment === 'uat' ? 'btn-success' : 'btn-outline-success' }}">
                                    <i class="fa fa-flask"></i> UAT/Staging
                                </a>
                            </div>
                        </div>

                        <div class="header-actions">
                            <a href="{{ route('device.matrix.create', ['environment' => $environment]) }}"
                               class="btn btn-primary btn-lg primary-action">
                                <i class="fa fa-plus"></i> Add New Entry
                            </a>
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-secondary dropdown-toggle"
                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fa fa-cog"></i> More Actions
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right">
                                    <li><a href="#" data-toggle="modal" data-target="#copyEnvironmentModal">
                                        <i class="fa fa-copy"></i> Copy Between Environments</a></li>
                                    <li role="separator" class="divider"></li>
                                    <li><a href="#" data-toggle="modal" data-target="#keyboardShortcutsModal">
                                        <i class="fa fa-keyboard-o"></i> Keyboard Shortcuts</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="quick-stats">
                        <div class="stat-item">
                            <span class="stat-number">{{ $entries->total() }}</span>
                            <span class="stat-label">Total Entries</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ $systemIds->count() }}</span>
                            <span class="stat-label">Systems</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ $countryCodes->count() }}</span>
                            <span class="stat-label">Countries</span>
                        </div>
                    </div>
                </div>
                
                <!-- Simplified Search and Filter Section -->
                <div class="search-filter-section">
                    <form action="{{ route('device.matrix.index') }}" method="GET" class="search-form">
                        <input type="hidden" name="environment" value="{{ $environment }}">

                        <!-- Primary Search -->
                        <div class="primary-search">
                            <div class="search-input-group">
                                <div class="input-group input-group-lg">
                                    <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                    <input type="text" name="search" id="search" class="form-control"
                                           value="{{ $search }}"
                                           placeholder="Search entries... (Ctrl+F)"
                                           autocomplete="off">
                                    @if($search)
                                    <span class="input-group-addon">
                                        <button type="button" class="btn btn-link clear-search" title="Clear search">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </span>
                                    @endif
                                </div>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fa fa-search"></i> Search
                                </button>
                            </div>
                        </div>

                        <!-- Advanced Filters (Collapsible) -->
                        <div class="advanced-filters">
                            <button type="button" class="btn btn-link filter-toggle" data-toggle="collapse" data-target="#advanced-filter-panel">
                                <i class="fa fa-filter"></i> Advanced Filters
                                <span class="filter-count">{{ (request('system_id') || request('country_code')) ? '(Active)' : '' }}</span>
                                <i class="fa fa-chevron-down"></i>
                            </button>

                            <div class="collapse {{ (request('system_id') || request('country_code')) ? 'in' : '' }}" id="advanced-filter-panel">
                                <div class="filter-panel">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="system_id">Filter by System</label>
                                                <select name="system_id" id="system_id" class="form-control">
                                                    <option value="">All Systems</option>
                                                    @foreach($systemIds as $system)
                                                    <option value="{{ $system->system_id }}" {{ request('system_id') == $system->system_id ? 'selected' : '' }}>
                                                        {{ $system->system_name }} ({{ $system->system_id }})
                                                    </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="country_code">Filter by Country</label>
                                                <select name="country_code" id="country_code" class="form-control">
                                                    <option value="">All Countries</option>
                                                    @foreach($countryCodes as $country)
                                                    <option value="{{ $country->country_code }}" {{ request('country_code') == $country->country_code ? 'selected' : '' }}>
                                                        {{ $country->country_name }} ({{ $country->country_code }})
                                                    </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="per_page">Results per page</label>
                                                <select name="per_page" id="per_page" class="form-control">
                                                    <option value="15" {{ $perPage == 15 ? 'selected' : '' }}>15</option>
                                                    <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                                                    <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                                                    <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="filter-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-filter"></i> Apply Filters
                                        </button>
                                        <a href="{{ route('device.matrix.index', ['environment' => $environment]) }}" class="btn btn-default">
                                            <i class="fa fa-refresh"></i> Clear All
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Results Summary and Actions -->
                <div class="results-section">
                    <div class="results-header">
                        <div class="results-info">
                            <h4>Device Matrix Entries</h4>
                            <span class="results-count">
                                @if($entries->total() > 0)
                                    Showing {{ $entries->firstItem() }} to {{ $entries->lastItem() }} of {{ $entries->total() }} entries
                                    @if($search || request('system_id') || request('country_code'))
                                        <small class="text-muted">(filtered)</small>
                                    @endif
                                @else
                                    No entries found
                                @endif
                            </span>
                        </div>

                        <div class="results-actions">
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-info dropdown-toggle" data-toggle="dropdown">
                                    <i class="fa fa-download"></i> Export <span class="selected-count"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right">
                                    <li>
                                        <a href="{{ route('device.matrix.export', ['environment' => $environment, 'system_id' => request('system_id'), 'country_code' => request('country_code'), 'search' => request('search')]) }}" id="export-all-btn">
                                            <i class="fa fa-file-excel-o"></i> Export Current Results
                                        </a>
                                    </li>
                                    <li id="export-selected-li" style="display: none;">
                                        <a href="#" id="export-selected-btn">
                                            <i class="fa fa-check-square-o"></i> Export Selected <span class="selected-count"></span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Selection Info Bar -->
                    <div class="selection-bar" style="display: none;">
                        <div class="selection-info">
                            <i class="fa fa-check-circle"></i>
                            <span class="selected-count-text">0 entries selected</span>
                        </div>
                        <div class="selection-actions">
                            <button type="button" class="btn btn-sm btn-outline-primary" id="bulk-actions-btn">
                                <i class="fa fa-edit"></i> Bulk Actions
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary clear-selection">
                                <i class="fa fa-times"></i> Clear Selection
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Data Table -->
                <form action="{{ route('device.matrix.bulk-update') }}" method="POST" id="entries-form">
                    @csrf
                    <input type="hidden" name="environment" value="{{ $environment }}">
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover sticky-header">
                            <thead>
                                <tr>
                                    <th width="30"><input type="checkbox" id="select-all"></th>
                                    <th class="sortable" data-sort="system_id">System ID</th>
                                    <th class="sortable" data-sort="system_name">System Name</th>
                                    <th class="sortable" data-sort="country_name">Country</th>
                                    <th class="sortable" data-sort="ultrasound">Ultrasound</th>
                                    <th class="sortable" data-sort="sherlock">Sherlock</th>
                                    <th class="sortable" data-sort="shell">Shell</th>
                                    <th class="sortable" data-sort="dicom">DICOM</th>
                                    <th width="150">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if($entries->count() > 0)
                                    @foreach($entries as $entry)
                                    <tr>
                                        <td><input type="checkbox" name="ids[]" value="{{ $entry->id }}" class="entry-checkbox"></td>
                                        <td>{{ $entry->system_id }}</td>
                                        <td>{{ $entry->system_name }}</td>
                                        <td>{{ $entry->country_name }} ({{ $entry->country_code }})</td>
                                        <td>{{ $entry->ultrasound }}</td>
                                        <td>{{ $entry->sherlock }}</td>
                                        <td>{{ $entry->shell }}</td>
                                        <td>{{ $entry->dicom }}</td>
                                        <td>
                                            <a href="{{ route('device.matrix.edit', ['id' => $entry->id, 'environment' => $environment]) }}" class="btn btn-sm btn-primary" style="color: white;">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger delete-btn" data-id="{{ $entry->id }}" data-toggle="modal" data-target="#deleteModal" style="color: white;">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="9" class="text-center">No entries found.</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $entries->appends(request()->except('page'))->links() }}
                    </div>
                    
                    <!-- Improved Bulk Actions Panel -->
                    @if($entries->count() > 0)
                    <div class="bulk-actions-panel" id="bulk-actions-panel" style="display: none;">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <i class="fa fa-edit"></i> Bulk Actions
                                    <button type="button" class="btn btn-xs btn-link pull-right" id="close-bulk-panel">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="bulk_action">Choose Action</label>
                                            <select name="bulk_action" id="bulk_action" class="form-control">
                                                <option value="">Select an action...</option>
                                                <option value="update">Update Field Values</option>
                                                <option value="delete">Delete Selected Entries</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-8" id="bulk-update-fields" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="bulk_field">Field to Update</label>
                                                    <select name="bulk_field" id="bulk_field" class="form-control">
                                                        <option value="">Select field...</option>
                                                        <optgroup label="Version Fields">
                                                            <option value="ultrasound">Ultrasound Version</option>
                                                            <option value="sherlock">Sherlock Version</option>
                                                            <option value="shell">Shell Version</option>
                                                            <option value="dicom">DICOM Version</option>
                                                        </optgroup>
                                                        <optgroup label="File Information">
                                                            <option value="source_filename">Source Filename</option>
                                                            <option value="download_filename">Download Filename</option>
                                                            <option value="product_image">Product Image</option>
                                                        </optgroup>
                                                        <optgroup label="Documentation">
                                                            <option value="installation_instructions">Installation Instructions</option>
                                                        </optgroup>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="bulk_value">New Value</label>
                                                    <input type="text" name="bulk_value" id="bulk_value" class="form-control" placeholder="Enter new value...">
                                                    <small class="form-text text-muted">This value will be applied to all selected entries</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="bulk-actions-footer">
                                    <div class="selected-summary">
                                        <span class="selected-count-text">0 entries selected</span>
                                    </div>
                                    <div class="action-buttons">
                                        <button type="submit" class="btn btn-primary" id="bulk-submit" disabled>
                                            <i class="fa fa-check"></i> Apply Changes
                                        </button>
                                        <button type="button" class="btn btn-default" id="close-bulk-panel">
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </form>
            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this device matrix entry? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="delete-form" action="" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <input type="hidden" name="environment" value="{{ $environment }}">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Copy Environment Modal -->
<div class="modal fade" id="copyEnvironmentModal" tabindex="-1" role="dialog" aria-labelledby="copyEnvironmentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="copyEnvironmentModalLabel">Copy Between Environments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('device.matrix.copy-environment') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <p>This feature allows you to copy device matrix entries between Production and UAT environments.</p>
                        <p><strong>Note:</strong> Existing entries in the target environment with the same system ID and country code will be updated.</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="source_environment">Source Environment</label>
                        <select name="source_environment" id="source_environment" class="form-control" required>
                            <option value="production" {{ $environment === 'production' ? 'selected' : '' }}>Production</option>
                            <option value="uat" {{ $environment === 'uat' ? 'selected' : '' }}>UAT/Staging</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="target_environment">Target Environment</label>
                        <select name="target_environment" id="target_environment" class="form-control" required>
                            <option value="production" {{ $environment !== 'production' ? 'selected' : '' }}>Production</option>
                            <option value="uat" {{ $environment !== 'uat' ? 'selected' : '' }}>UAT/Staging</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="copy_selected_container" style="display: none;">
                        <div class="alert alert-info">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="copy_selected_only" name="copy_selected_only">
                                <label class="custom-control-label" for="copy_selected_only">
                                    <strong>Copy only selected entries (<span id="copy_selected_count">0</span>)</strong>
                                </label>
                            </div>
                            <small class="form-text">Only the entries you've selected in the table will be copied.</small>
                        </div>
                    </div>
                    
                    <div class="form-group" id="copy_all_container">
                        <div class="alert alert-warning">
                            <strong>Copy all entries from source environment</strong>
                            <small class="form-text">All entries from the source environment will be copied to the target environment.</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Copy Entries</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Handle select all checkbox
        $('#select-all').change(function() {
            $('.entry-checkbox').prop('checked', $(this).prop('checked'));
        });
        
        // Handle bulk action display
        $('#bulk_action').change(function() {
            if ($(this).val() === 'update') {
                $('#bulk-update-fields').show();
            } else {
                $('#bulk-update-fields').hide();
            }
        });
        
        // Validate bulk action form before submit
        $('#bulk-submit').click(function(e) {
            e.preventDefault();
            
            // Check if any checkboxes are selected
            if ($('.entry-checkbox:checked').length === 0) {
                alert('Please select at least one entry.');
                return;
            }
            
            // Check if an action is selected
            if ($('#bulk_action').val() === '') {
                alert('Please select an action to perform.');
                return;
            }
            
            // If update action, validate fields
            if ($('#bulk_action').val() === 'update') {
                if ($('#bulk_field').val() === '') {
                    alert('Please select a field to update.');
                    return;
                }
            }
            
            // If delete action, confirm
            if ($('#bulk_action').val() === 'delete') {
                if (!confirm('Are you sure you want to delete the selected entries? This action cannot be undone.')) {
                    return;
                }
            }
            
            // Submit the form
            $('#entries-form').submit();
        });
        
        // Handle delete button click
        $('.delete-btn').click(function() {
            var id = $(this).data('id');
            var url = "{{ route('device.matrix.destroy', ['id' => ':id']) }}";
            url = url.replace(':id', id);
            $('#delete-form').attr('action', url);
        });
        
        // Auto-submit form when per_page changes
        $('#per_page').change(function() {
            $('.search-form').submit();
        });
        
        // Update selection count
        function updateSelectionCount() {
            var count = $('.entry-checkbox:checked').length;

            // Update the count display
            $('.selected-count').text(count > 0 ? '(' + count + ')' : '');
            $('.selected-count-text').text(count + ' ' + (count === 1 ? 'entry' : 'entries') + ' selected');

            // Show/hide the selection bar and export selected option
            if (count > 0) {
                $('.selection-bar').show();
                $('#export-selected-li').show();
                $('#bulk-submit').prop('disabled', false);
            } else {
                $('.selection-bar').hide();
                $('#export-selected-li').hide();
                $('#bulk-submit').prop('disabled', true);
                $('#bulk-actions-panel').hide();
            }

            // Update the export selected button text
            $('#export-selected-btn .selected-count').text('(' + count + ')');

            // Update copy environment modal
            $('#copy_selected_count').text(count);
            if (count > 0) {
                $('#copy_selected_container').show();
            } else {
                $('#copy_selected_container').hide();
                $('#copy_selected_only').prop('checked', false);
            }
        }
        
        // Handle checkbox changes
        $('.entry-checkbox').change(function() {
            // Update row highlighting
            $(this).closest('tr').toggleClass('selected-row', this.checked);
            
            // Update selection count
            updateSelectionCount();
        });
        
        // Handle select all checkbox
        $('#select-all').change(function() {
            var isChecked = $(this).prop('checked');
            $('.entry-checkbox').prop('checked', isChecked);
            
            // Update row highlighting for all rows
            $('.entry-checkbox').closest('tr').toggleClass('selected-row', isChecked);
            
            updateSelectionCount();
        });
        
        // Clear selection button
        $('.clear-selection').click(function() {
            $('.entry-checkbox').prop('checked', false);
            $('#select-all').prop('checked', false);
            
            // Clear row highlighting
            $('.entry-checkbox').closest('tr').removeClass('selected-row');
            
            updateSelectionCount();
        });
        
        // Handle export selected button click
        $('#export-selected-btn').click(function(e) {
            e.preventDefault();
            
            // Check if any entries are selected
            if ($('.entry-checkbox:checked').length === 0) {
                alert('Please select at least one entry to export.');
                return;
            }
            
            // Create a form to submit selected IDs
            var $form = $('<form>', {
                'action': $('#export-all-btn').attr('href'),
                'method': 'GET',
                'style': 'display: none'
            });
            
            // Add environment parameter
            $form.append($('<input>', {
                'name': 'environment',
                'value': '{{ $environment }}'
            }));
            
            // Add selected IDs
            $('.entry-checkbox:checked').each(function() {
                $form.append($('<input>', {
                    'name': 'selected_ids[]',
                    'value': $(this).val()
                }));
            });
            
            // Submit the form
            $('body').append($form);
            $form.submit();
        });
        
        // Initialize selection count on page load
        updateSelectionCount();

        // Handle bulk actions button
        $('#bulk-actions-btn').click(function() {
            $('#bulk-actions-panel').slideToggle();
        });

        // Handle close bulk panel
        $('#close-bulk-panel').click(function() {
            $('#bulk-actions-panel').slideUp();
        });

        // Handle filter toggle
        $('.filter-toggle').click(function() {
            var icon = $(this).find('.fa-chevron-down, .fa-chevron-up');
            if (icon.hasClass('fa-chevron-down')) {
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            } else {
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
            }
        });
        
        // Handle clear search button
        $('.clear-search').click(function() {
            $('#search').val('');
            // Submit the form to refresh results
            $('.search-form').submit();
        });
        
        // Add keyboard shortcuts
        $(document).keydown(function(e) {
            // Ctrl+F or Cmd+F for search
            if ((e.ctrlKey || e.metaKey) && e.keyCode === 70) { // Ctrl+F or Cmd+F
                e.preventDefault(); // Prevent browser's default search
                $('#search').focus();
                
                // Scroll to search if not visible
                if (!isElementInViewport($('#search')[0])) {
                    $('html, body').animate({
                        scrollTop: $('#search').offset().top - 100
                    }, 200);
                }
            }
            
            // Alt+Q or Option+Q for quick filter (Mac compatibility)
            if ((e.altKey || e.originalEvent.altKey) && (e.keyCode === 81 || e.key === 'q')) { // Alt+Q or Option+Q
                e.preventDefault();
                $('#quick-filter').focus();
                
                // Scroll to quick filter if not visible
                if (!isElementInViewport($('#quick-filter')[0])) {
                    $('html, body').animate({
                        scrollTop: $('#quick-filter').offset().top - 100
                    }, 200);
                }
            }
            
            // Escape key to clear filters
            if (e.keyCode === 27) { // Escape
                // If quick filter has focus and has value, clear it
                if ($('#quick-filter').is(':focus') && $('#quick-filter').val()) {
                    $('#quick-filter').val('').trigger('input');
                }
                // If search has focus and has value, clear it
                else if ($('#search').is(':focus') && $('#search').val()) {
                    $('#search').val('');
                }
            }
            
            // Question mark key to show keyboard shortcuts
            if ((e.keyCode === 191 || e.key === '?') && !$(e.target).is('input, textarea, select')) { // ? key
                e.preventDefault();
                $('#keyboardShortcutsModal').modal('show');
            }

            // Ctrl+A or Cmd+A to select all
            if ((e.ctrlKey || e.metaKey) && e.keyCode === 65 && !$(e.target).is('input, textarea, select')) { // Ctrl+A or Cmd+A
                e.preventDefault();
                $('#select-all').prop('checked', true).trigger('change');
            }

            // Ctrl+D or Cmd+D to deselect all
            if ((e.ctrlKey || e.metaKey) && e.keyCode === 68 && !$(e.target).is('input, textarea, select')) { // Ctrl+D or Cmd+D
                e.preventDefault();
                $('.clear-selection').trigger('click');
            }
        });
        
        // Helper function to check if element is in viewport
        function isElementInViewport(el) {
            var rect = el.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }
        
        // Add visual cue for search shortcut
        $('#search').attr('title', 'Press Ctrl+F or Cmd+F to search');
        
        // Add animation to search icon on page load
        $('.input-group-prepend .fa-search').addClass('search-pulse');
        
        // Loading overlay management with auto-hide
        function showLoading(message) {
            $('.loading-text').text(message || 'Loading...');
            $('.loading-overlay').addClass('show');
        }

        function hideLoading() {
            $('.loading-overlay').removeClass('show');
        }

        // Auto-hide loading after a timeout
        function showLoadingWithTimeout(message, timeout) {
            showLoading(message);
            setTimeout(function() {
                hideLoading();
            }, timeout || 3000);
        }

        // Show loading on form submissions
        $('.search-form').on('submit', function() {
            showLoading('Searching...');
        });

        // Show loading on export actions with auto-hide
        $('#export-all-btn').on('click', function() {
            showLoadingWithTimeout('Exporting data...', 5000);
        });

        $('#export-selected-btn').on('click', function() {
            showLoadingWithTimeout('Exporting selected entries...', 5000);
        });

        // Show loading on page navigation
        $('.pagination a').on('click', function() {
            showLoading('Loading page...');
        });

        // Show loading on environment toggle
        $('.environment-toggle a').on('click', function() {
            showLoading('Switching environment...');
        });
        
        // Quick filter functionality with improved highlighting
        $('#quick-filter').on('input', function() {
            var value = $(this).val().toLowerCase().trim();
            var visibleRows = 0;

            // Show/hide clear button
            if (value) {
                $('#clear-quick-filter-addon').show();
            } else {
                $('#clear-quick-filter-addon').hide();
            }

            // Add no-results message if it doesn't exist
            if ($('.no-results-message').length === 0) {
                $('<div class="no-results-message"><div class="alert alert-info"><i class="fa fa-info-circle"></i> No entries match your quick filter. <button type="button" class="btn btn-sm btn-outline-secondary ml-3" id="clear-quick-filter-msg"><i class="fa fa-times"></i> Clear Filter</button></div></div>')
                    .insertAfter('.table-responsive');
            }

            // Clear all existing highlights first
            $('table tbody tr').each(function() {
                $(this).find('.quick-filter-highlight').each(function() {
                    var parent = $(this).parent();
                    parent.html(parent.text());
                });
            });

            // Filter table rows
            $('table tbody tr').each(function() {
                var $row = $(this);
                var rowText = $row.text().toLowerCase();
                var match = !value || rowText.indexOf(value) > -1;

                $row.toggle(match);

                if (match) {
                    visibleRows++;

                    // Add highlighting only if there's a filter value
                    if (value) {
                        $row.find('td').each(function() {
                            var $cell = $(this);
                            if (!$cell.has('button, a, input').length) {
                                var text = $cell.text();
                                var lowerText = text.toLowerCase();
                                if (lowerText.indexOf(value) > -1) {
                                    var regex = new RegExp('(' + escapeRegex(value) + ')', 'gi');
                                    $cell.html(text.replace(regex, '<span class="quick-filter-highlight">$1</span>'));
                                }
                            }
                        });
                    }
                }
            });

            // Show/hide no results message
            if (visibleRows === 0 && value) {
                $('.no-results-message').show();
                $('.quick-filter-count').text('');
            } else {
                $('.no-results-message').hide();

                // Update the count indicator
                var totalRows = $('table tbody tr').length;
                if (value) {
                    $('.quick-filter-count').html(' &mdash; <strong>' + visibleRows + '</strong> of <strong>' + totalRows + '</strong> entries shown');
                } else {
                    $('.quick-filter-count').text('');
                }
            }
        });

        // Helper function to escape regex special characters
        function escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }
        
        // Clear quick filter - handle both buttons
        $(document).on('click', '.clear-quick-filter, #clear-quick-filter-msg', function() {
            $('#quick-filter').val('').trigger('input');
            $('#quick-filter').focus();
        });
        
        // Back to top button functionality
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $('.back-to-top').addClass('show');
            } else {
                $('.back-to-top').removeClass('show');
            }
        });
        
        $('.back-to-top').click(function() {
            $('html, body').animate({scrollTop: 0}, 500);
            return false;
        });
        
        // Table sorting functionality
        $('.sortable').click(function() {
            var table = $(this).parents('table').eq(0);
            var rows = table.find('tr:gt(0)').toArray().sort(comparer($(this).index()));
            this.asc = !this.asc;
            
            // Update sort indicators
            $('.sortable').removeClass('asc desc');
            if (this.asc) {
                $(this).addClass('asc');
            } else {
                $(this).addClass('desc');
                rows = rows.reverse();
            }
            
            // Reorder the table
            for (var i = 0; i < rows.length; i++) {
                table.append(rows[i]);
            }
            
            // Maintain row highlighting for selected rows
            $('.entry-checkbox:checked').closest('tr').addClass('selected-row');
            
            return false;
        });
        
        function comparer(index) {
            return function(a, b) {
                var valA = getCellValue(a, index);
                var valB = getCellValue(b, index);
                
                // Handle numeric values
                if (!isNaN(valA) && !isNaN(valB)) {
                    return valA - valB;
                }
                
                // Handle text values
                return valA.localeCompare(valB);
            };
        }
        
        function getCellValue(row, index) {
            var cell = $(row).children('td').eq(index);
            return cell.text().trim();
        }
        
        // Focus search field if 'q' parameter is in URL (indicating a search was performed)
        if (window.location.search.indexOf('search=') > -1) {
            // Highlight the search box
            $('#search').addClass('border-primary');
            
            // Remove highlight after a delay
            setTimeout(function() {
                $('#search').removeClass('border-primary');
            }, 2000);
        }
        
        // Handle search field placeholder change on focus/blur
        $('#search').on('focus', function() {
            // Store the original placeholder
            $(this).data('original-placeholder', $(this).attr('placeholder'));
            
            // Change to the focused placeholder
            if ($(this).data('focused-placeholder')) {
                $(this).attr('placeholder', $(this).data('focused-placeholder'));
            }
        }).on('blur', function() {
            // Restore the original placeholder
            if ($(this).data('original-placeholder')) {
                $(this).attr('placeholder', $(this).data('original-placeholder'));
            }
        });
        
        // Handle copy environment modal open
        $('#copyEnvironmentModal').on('show.bs.modal', function() {
            var count = $('.entry-checkbox:checked').length;
            
            // Update the copy environment modal to reflect selection
            if (count > 0) {
                $('#copy_selected_count').text(count);
                $('#copy_selected_container').show();
                $('#copy_selected_only').prop('checked', true).trigger('change');
            } else {
                $('#copy_selected_container').hide();
                $('#copy_selected_only').prop('checked', false).trigger('change');
            }
        });
        
        // Handle copy environment form
        $('#copy_selected_only').change(function() {
            // Remove any previously added selected IDs
            $('#copyEnvironmentModal form input[name^="selected_ids"]').remove();
            
            if ($(this).is(':checked')) {
                // Check if any entries are selected
                if ($('.entry-checkbox:checked').length === 0) {
                    alert('Please select at least one entry to copy.');
                    $(this).prop('checked', false);
                    return;
                }
                
                // Add selected IDs to the form
                $('.entry-checkbox:checked').each(function() {
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'selected_ids[]',
                        value: $(this).val()
                    }).appendTo('#copyEnvironmentModal form');
                });
                
                // Show/hide appropriate containers
                $('#copy_all_container').hide();
            } else {
                // Show/hide appropriate containers
                $('#copy_all_container').show();
            }
        });
        
        // Prevent source and target from being the same
        $('#source_environment, #target_environment').change(function() {
            var source = $('#source_environment').val();
            var target = $('#target_environment').val();
            
            if (source === target) {
                alert('Source and target environments cannot be the same.');
                if (this.id === 'source_environment') {
                    $('#target_environment').val(source === 'production' ? 'uat' : 'production');
                } else {
                    $('#source_environment').val(target === 'production' ? 'uat' : 'production');
                }
            }
        });
    });
</script>
@endsection