<?php
// Set The Title
$category_header = $title = 'Software Lot Numbers Management';
$menu = 'software_lot_numbers';
?>

@extends('layouts.admin.master')
@section('title', $title)
@section('styles')
@if($softwareLotNumbers->count() < 100)
<style>
    .dataTables_paginate {
        display: none;
    }
</style>
@endif
@stop

@section('content')
<div id="content_hldr" class="">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>{!! $title !!}</h1>
            </div>
        </div>
    </div>

    <!-- INCLUDE THE SIDEBAR -->
    @include('admin/elabeling/includes/sidebar')

    <div id="content">
        <!--pagecontent-->
        <div id="pagecontent">
            <div id="main">
                <div class="row">
                    <div class="col-md-8">
                        <h4 style="margin-top: 0px;">Software Lot Numbers</h4>
                    </div>
                    <div class="col-md-4 text-right">
                        <a href="{{ route('elabeling.software.lot.numbers.create') }}" class="btn btn-primary">
                            <i class="fa fa-plus"></i> Add Software Lot Number
                        </a>
                    </div>
                </div>

                <!-- CHECK FOR FLASHED MESSAGE -->
                @include('flash::message')

                <!-- Search Form -->
                <div class="row" style="margin-bottom: 20px;">
                    <div class="col-md-12">
                        <form method="GET" action="{{ route('elabeling.software.lot.numbers.index') }}" class="form-inline">
                            <div class="form-group">
                                <input type="text" name="search" value="{{ $search }}" 
                                       placeholder="Search lot numbers, part numbers, product codes..." 
                                       class="form-control" style="width: 300px;">
                            </div>
                            <div class="form-group">
                                <select name="per_page" class="form-control">
                                    <option value="15" {{ $perPage == 15 ? 'selected' : '' }}>15 per page</option>
                                    <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25 per page</option>
                                    <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50 per page</option>
                                    <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100 per page</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-default">
                                <i class="fa fa-search"></i> Search
                            </button>
                            @if($search)
                            <a href="{{ route('elabeling.software.lot.numbers.index') }}" class="btn btn-link">
                                <i class="fa fa-times"></i> Clear
                            </a>
                            @endif
                        </form>
                    </div>
                </div>

                @if ($softwareLotNumbers->count() > 0)

                <div id="statusMessage" class="alert alert-success" style="display:none;margin-top:30px;"></div>

                <div id="datatables_container">
                    <table id="myTable" class="display table table-striped" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th style="text-align:left;">Lot Number</th>
                                <th style="text-align:left;">Part Number</th>
                                <th style="text-align:left;">Product Code</th>
                                <th style="text-align:left;">Product Name</th>
                                <th style="text-align:left;">Manufacture Date</th>
                                <th style="text-align:left;">Status</th>
                                @if (Auth::user()->hasPermissionTo('eliterature edit'))
                                <th style="text-align:left;">Actions</th>
                                @endif
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($softwareLotNumbers as $lotNumber)
                            <tr id="tr{{ $lotNumber->id }}" class="list_item">
                                <td>{{ $lotNumber->lot_number }}</td>
                                <td>{{ $lotNumber->part_number }}</td>
                                <td>{{ $lotNumber->product_code }}</td>
                                <td>{{ $lotNumber->product_name }}</td>
                                <td>{{ $lotNumber->formatted_manufacture_date }}</td>
                                <td>
                                    @if($lotNumber->deleted_at)
                                    <span class="label label-danger">Inactive</span>
                                    @else
                                    <span class="label label-success">Active</span>
                                    @endif
                                </td>
                                @if (Auth::user()->hasPermissionTo('eliterature edit'))
                                <td>
                                    @if (Auth::user()->hasPermissionTo('eliterature delete'))
                                    <span class="table_icon">
                                        <a id="update-status-{{ $lotNumber->id }}" 
                                           onclick="recordStatus('update', '{{ route('elabeling.software.lot.numbers.change.status', [$lotNumber->id]) }}', {id: {{ $lotNumber->id }}, _token: '{{ csrf_token() }}'})">
                                            <i class="fa fa-power-off status-btn" id="updateStatusIcon-{{ $lotNumber->id }}"></i>
                                        </a>
                                    </span>
                                    @endif
                                    
                                    <span class="edit_icon table_icon">
                                        <a href="{{ route('elabeling.software.lot.numbers.edit', [$lotNumber->id]) }}">
                                            <i class="fa fa-pencil edit-btn" id="updateEditIcon-{{ $lotNumber->id }}"></i>
                                        </a>
                                    </span>
                                    
                                    @if (Auth::user()->hasPermissionTo('eliterature delete'))
                                    <span class="table_icon">
                                        <a href="#" onclick="if(confirm('Are you sure you want to delete this software lot number?')) { document.getElementById('delete-form-{{ $lotNumber->id }}').submit(); }">
                                            <i class="fa fa-trash delete-btn"></i>
                                        </a>
                                        <form id="delete-form-{{ $lotNumber->id }}" action="{{ route('elabeling.software.lot.numbers.destroy', [$lotNumber->id]) }}" method="POST" style="display: none;">
                                            @csrf
                                            @method('DELETE')
                                        </form>
                                    </span>
                                    @endif
                                </td>
                                @endif
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="row">
                    <div class="col-md-12">
                        {{ $softwareLotNumbers->appends(request()->query())->links() }}
                    </div>
                </div>

                @else

                <br />
                <div class="alert alert-warning">
                    @if($search)
                    No software lot numbers found matching your search criteria.
                    @else
                    There are no software lot numbers found.
                    @endif
                </div>

                @endif

            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>
@stop

@section('scripts')
<script type="text/javascript">
    $(document).ready(function() {
        // datatable for the chart
        $('#myTable').dataTable({
            "responsive": true,
            "paging": false,
            "info": false,
            "searching": false,
            "lengthMenu": [
                [100, 150, 200, -1],
                [100, 150, 200, "All"]
            ]
        });
    });

    function recordStatus(action, url, data) {
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    $('#statusMessage').html(response.message).show().delay(3000).fadeOut();
                    // Reload the page to reflect changes
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                }
            },
            error: function(xhr) {
                alert('Error: ' + xhr.responseJSON.message);
            }
        });
    }
</script>
@stop
