<?php
// Set The Title
$category_header = $title = 'View Software Lot Number';
$menu = 'software_lot_numbers';
?>

@extends('layouts.admin.master')
@section('title', $title)

@section('content')
<div id="content_hldr" class="">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>{!! $title !!}</h1>
            </div>
        </div>
    </div>

    <!-- INCLUDE THE SIDEBAR -->
    @include('admin/elabeling/includes/sidebar')

    <div id="content">
        <!--pagecontent-->
        <div id="pagecontent">
            <div id="main">
                <div class="row">
                    <div class="col-md-8">
                        <h4 style="margin-top: 0px;">Software Lot Number: {{ $softwareLotNumber->lot_number }}</h4>
                    </div>
                    <div class="col-md-4 text-right">
                        <a href="{{ route('elabeling.software.lot.numbers.index') }}" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> Back to List
                        </a>
                        <a href="{{ route('elabeling.software.lot.numbers.edit', $softwareLotNumber->id) }}" class="btn btn-primary">
                            <i class="fa fa-edit"></i> Edit
                        </a>
                    </div>
                </div>

                <!-- CHECK FOR FLASHED MESSAGE -->
                @include('flash::message')

                <div class="row">
                    <div class="col-md-8">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">Software Lot Number Details</h3>
                            </div>
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="200">Lot Number:</th>
                                        <td>{{ $softwareLotNumber->lot_number }}</td>
                                    </tr>
                                    <tr>
                                        <th>Part Number:</th>
                                        <td>{{ $softwareLotNumber->part_number }}</td>
                                    </tr>
                                    <tr>
                                        <th>Product Code:</th>
                                        <td>{{ $softwareLotNumber->product_code ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Product Name:</th>
                                        <td>{{ $softwareLotNumber->product_name ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Reference:</th>
                                        <td>{{ $softwareLotNumber->ref ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Resource Type:</th>
                                        <td>{{ $softwareLotNumber->resource_type ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Manufacture Date:</th>
                                        <td>{{ $softwareLotNumber->formatted_manufacture_date ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Sequence Number:</th>
                                        <td>{{ $softwareLotNumber->sequence_number ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Status:</th>
                                        <td>
                                            @if($softwareLotNumber->deleted_at)
                                            <span class="label label-danger">Inactive</span>
                                            @else
                                            <span class="label label-success">Active</span>
                                            @endif
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">Record Information</h3>
                            </div>
                            <div class="panel-body">
                                <p><strong>ID:</strong> {{ $softwareLotNumber->id }}</p>
                                <p><strong>Created:</strong> {{ $softwareLotNumber->created_at->format('Y-m-d H:i:s') }}</p>
                                <p><strong>Last Updated:</strong> {{ $softwareLotNumber->updated_at->format('Y-m-d H:i:s') }}</p>
                                @if($softwareLotNumber->deleted_at)
                                <p><strong>Deactivated:</strong> {{ $softwareLotNumber->deleted_at->format('Y-m-d H:i:s') }}</p>
                                @endif
                            </div>
                        </div>
                        
                        <div class="panel panel-warning">
                            <div class="panel-heading">
                                <h3 class="panel-title">Actions</h3>
                            </div>
                            <div class="panel-body">
                                <a href="{{ route('elabeling.software.lot.numbers.edit', $softwareLotNumber->id) }}" class="btn btn-primary btn-block">
                                    <i class="fa fa-edit"></i> Edit Record
                                </a>
                                
                                @if (Auth::user()->hasPermissionTo('eliterature delete'))
                                <hr>
                                @if($softwareLotNumber->deleted_at)
                                <button class="btn btn-success btn-block" onclick="recordStatus('update', '{{ route('elabeling.software.lot.numbers.change.status', [$softwareLotNumber->id]) }}', {id: {{ $softwareLotNumber->id }}, _token: '{{ csrf_token() }}'})">
                                    <i class="fa fa-check"></i> Activate Record
                                </button>
                                @else
                                <button class="btn btn-warning btn-block" onclick="recordStatus('update', '{{ route('elabeling.software.lot.numbers.change.status', [$softwareLotNumber->id]) }}', {id: {{ $softwareLotNumber->id }}, _token: '{{ csrf_token() }}'})">
                                    <i class="fa fa-ban"></i> Deactivate Record
                                </button>
                                @endif
                                
                                <button class="btn btn-danger btn-block" onclick="if(confirm('Are you sure you want to permanently delete this software lot number?')) { document.getElementById('delete-form').submit(); }">
                                    <i class="fa fa-trash"></i> Delete Permanently
                                </button>
                                
                                <form id="delete-form" action="{{ route('elabeling.software.lot.numbers.destroy', [$softwareLotNumber->id]) }}" method="POST" style="display: none;">
                                    @csrf
                                    @method('DELETE')
                                </form>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>
@stop

@section('scripts')
<script type="text/javascript">
    function recordStatus(action, url, data) {
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    // Reload the page to reflect changes
                    location.reload();
                }
            },
            error: function(xhr) {
                alert('Error: ' + xhr.responseJSON.message);
            }
        });
    }
</script>
@stop
